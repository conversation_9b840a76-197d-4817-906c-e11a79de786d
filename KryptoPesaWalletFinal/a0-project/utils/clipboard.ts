import Clipboard from '@react-native-clipboard/clipboard';

/**
 * Clipboard utility using the new @react-native-clipboard/clipboard package
 */
export const clipboardUtils = {
  /**
   * Copy text to clipboard
   * @param text - Text to copy
   */
  setString: async (text: string): Promise<void> => {
    try {
      await Clipboard.setString(text);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      throw error;
    }
  },

  /**
   * Get text from clipboard
   */
  getString: async (): Promise<string> => {
    try {
      const result = await Clipboard.getString();
      return result;
    } catch (error) {
      console.error('Failed to get from clipboard:', error);
      throw error;
    }
  }
};
