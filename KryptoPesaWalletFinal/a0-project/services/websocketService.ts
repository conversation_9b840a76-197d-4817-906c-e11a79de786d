import { authTokens } from './api';
import { ENV_CONFIG, log, logError, logWarn } from '../config/environment';

// WebSocket event types
export type WebSocketEventType =
  | 'trade_update'
  | 'offer_update'
  | 'chat_message'
  | 'notification'
  | 'price_update'
  | 'user_status'
  | 'escrow_update';

export interface WebSocketEvent {
  type: WebSocketEventType;
  data: any;
  timestamp: string;
}

export interface TradeUpdateEvent {
  tradeId: string;
  status: string;
  timeline: Array<{
    status: string;
    timestamp: string;
    note?: string;
  }>;
}

export interface ChatMessageEvent {
  chatId: string;
  message: {
    _id: string;
    sender: {
      _id: string;
      username: string;
    };
    content: string;
    type: 'text' | 'image' | 'file' | 'system';
    timestamp: string;
    attachments?: Array<{
      type: string;
      url: string;
      name: string;
    }>;
  };
}

export interface NotificationEvent {
  _id: string;
  type: 'trade' | 'offer' | 'chat' | 'system' | 'security';
  title: string;
  message: string;
  data?: any;
  isRead: boolean;
  createdAt: string;
}

export interface PriceUpdateEvent {
  cryptocurrency: string;
  fiatCurrency: string;
  price: number;
  change24h: number;
  timestamp: string;
}

// WebSocket Service Class
class WebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = ENV_CONFIG.WEBSOCKET_RECONNECT_ATTEMPTS;
  private reconnectDelay = ENV_CONFIG.WEBSOCKET_RECONNECT_DELAY; // Start with configured delay
  private isConnecting = false;
  private eventListeners: Map<WebSocketEventType, Set<(data: any) => void>> = new Map();
  private connectionListeners: Set<(connected: boolean) => void> = new Set();

  private readonly WS_URL = ENV_CONFIG.WS_BASE_URL;

  /**
   * Connect to WebSocket server
   */
  async connect(): Promise<void> {
    if (this.ws?.readyState === WebSocket.OPEN || this.isConnecting) {
      return;
    }

    this.isConnecting = true;

    try {
      const token = await authTokens.getAccessToken();
      const walletAddress = await authTokens.getWalletAddress();

      if (!token || !walletAddress) {
        throw new Error('No authentication token or wallet address available');
      }

      const wsUrl = `${this.WS_URL}?token=${encodeURIComponent(token)}&wallet=${encodeURIComponent(walletAddress)}`;

      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        log('WebSocket connected');
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        this.reconnectDelay = ENV_CONFIG.WEBSOCKET_RECONNECT_DELAY;
        this.notifyConnectionListeners(true);
      };

      this.ws.onmessage = (event) => {
        try {
          const wsEvent: WebSocketEvent = JSON.parse(event.data);
          this.handleEvent(wsEvent);
        } catch (error) {
          logError('Failed to parse WebSocket message:', error);
        }
      };

      this.ws.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        this.isConnecting = false;
        this.notifyConnectionListeners(false);

        // Attempt to reconnect if not a normal closure
        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnect();
        }
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.isConnecting = false;
      };

    } catch (error) {
      console.error('WebSocket connection error:', error);
      this.isConnecting = false;
      throw error;
    }
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    this.reconnectAttempts = this.maxReconnectAttempts; // Prevent reconnection
  }

  /**
   * Check if WebSocket is connected
   */
  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  /**
   * Send message through WebSocket
   */
  send(type: string, data: any): void {
    if (!this.isConnected()) {
      logWarn('WebSocket not connected, cannot send message');
      return;
    }

    const message = {
      type,
      data,
      timestamp: new Date().toISOString(),
    };

    this.ws!.send(JSON.stringify(message));
  }

  /**
   * Subscribe to specific event type
   */
  on(eventType: WebSocketEventType, callback: (data: any) => void): () => void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, new Set());
    }

    this.eventListeners.get(eventType)!.add(callback);

    // Return unsubscribe function
    return () => {
      this.eventListeners.get(eventType)?.delete(callback);
    };
  }

  /**
   * Subscribe to connection status changes
   */
  onConnectionChange(callback: (connected: boolean) => void): () => void {
    this.connectionListeners.add(callback);

    // Return unsubscribe function
    return () => {
      this.connectionListeners.delete(callback);
    };
  }

  /**
   * Subscribe to trade updates
   */
  onTradeUpdate(callback: (data: TradeUpdateEvent) => void): () => void {
    return this.on('trade_update', callback);
  }

  /**
   * Subscribe to chat messages
   */
  onChatMessage(callback: (data: ChatMessageEvent) => void): () => void {
    return this.on('chat_message', callback);
  }

  /**
   * Subscribe to notifications
   */
  onNotification(callback: (data: NotificationEvent) => void): () => void {
    return this.on('notification', callback);
  }

  /**
   * Subscribe to price updates
   */
  onPriceUpdate(callback: (data: PriceUpdateEvent) => void): () => void {
    return this.on('price_update', callback);
  }

  /**
   * Join a specific room (e.g., trade room, chat room)
   */
  joinRoom(roomType: 'trade' | 'chat' | 'offers', roomId: string): void {
    this.send('join_room', { roomType, roomId });
  }

  /**
   * Leave a specific room
   */
  leaveRoom(roomType: 'trade' | 'chat' | 'offers', roomId: string): void {
    this.send('leave_room', { roomType, roomId });
  }

  /**
   * Send chat message
   */
  sendChatMessage(chatId: string, content: string, type: 'text' | 'image' | 'file' = 'text'): void {
    this.send('chat_message', {
      chatId,
      content,
      type,
    });
  }

  /**
   * Update user online status
   */
  updateUserStatus(status: 'online' | 'away' | 'offline'): void {
    this.send('user_status', { status });
  }

  /**
   * Handle incoming WebSocket events
   */
  private handleEvent(event: WebSocketEvent): void {
    const listeners = this.eventListeners.get(event.type);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(event.data);
        } catch (error) {
          console.error(`Error in WebSocket event listener for ${event.type}:`, error);
        }
      });
    }
  }

  /**
   * Notify connection status listeners
   */
  private notifyConnectionListeners(connected: boolean): void {
    this.connectionListeners.forEach(callback => {
      try {
        callback(connected);
      } catch (error) {
        console.error('Error in connection status listener:', error);
      }
    });
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnect(): void {
    this.reconnectAttempts++;

    setTimeout(() => {
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      this.connect().catch(error => {
        console.error('Reconnection failed:', error);
      });
    }, this.reconnectDelay);

    // Exponential backoff with max delay of 30 seconds
    this.reconnectDelay = Math.min(this.reconnectDelay * 2, 30000);
  }
}

// Create and export service instance
export const websocketService = new WebSocketService();

// WebSocket hooks for React components
export const useWebSocket = () => {
  return {
    connect: () => websocketService.connect(),
    disconnect: () => websocketService.disconnect(),
    isConnected: () => websocketService.isConnected(),
    on: (eventType: WebSocketEventType, callback: (data: any) => void) =>
      websocketService.on(eventType, callback),
    onConnectionChange: (callback: (connected: boolean) => void) =>
      websocketService.onConnectionChange(callback),
    joinRoom: (roomType: 'trade' | 'chat' | 'offers', roomId: string) =>
      websocketService.joinRoom(roomType, roomId),
    leaveRoom: (roomType: 'trade' | 'chat' | 'offers', roomId: string) =>
      websocketService.leaveRoom(roomType, roomId),
    sendChatMessage: (chatId: string, content: string, type?: 'text' | 'image' | 'file') =>
      websocketService.sendChatMessage(chatId, content, type),
  };
};

export default websocketService;
