import { API_ENDPOINTS, ApiResponse, EAST_AFRICAN_CONFIG } from './api';
import { log, logError, logWarn } from '../config/environment';

// Lazy import for apiClient to avoid early initialization
const getApiClient = () => {
  const { apiClient } = require('./api');
  return apiClient;
};

// Wallet types
export interface WalletBalance {
  currency: string;
  name: string;
  balance: number;
  pendingBalance: number;
  usdValue: number;
  address: string;
  change: number;
  network: string;
}

export interface Transaction {
  _id: string;
  type: 'send' | 'receive' | 'trade' | 'escrow';
  cryptocurrency: string;
  amount: number;
  usdValue: number;
  status: 'pending' | 'confirmed' | 'failed';
  txHash?: string;
  fromAddress?: string;
  toAddress?: string;
  networkFee: number;
  tradeId?: string;
  description?: string;
  timestamp: string;
  confirmations?: number;
  requiredConfirmations?: number;
}

export interface WalletSecurity {
  backupStatus: 'not_backed_up' | 'backed_up' | 'verified';
  lastBackupDate?: string;
  securityScore: number;
  recommendations: string[];
}

export interface WalletStats {
  totalUsdValue: number;
  totalTrades: number;
  totalVolume: number;
  profitLoss: number;
  profitLossPercentage: number;
}

// Wallet Service Class
class WalletService {
  /**
   * Get wallet balances for all supported cryptocurrencies
   */
  async getWalletBalances(): Promise<WalletBalance[]> {
    try {
      // Check if user is authenticated before making API call
      const isAuth = await getApiClient().isAuthenticated();
      if (!isAuth) {
        // Silently return mock data without error logging for unauthenticated users
        logWarn('User not authenticated, returning mock wallet balances');
        return this.getMockWalletBalances();
      }

      log('Fetching wallet balances from backend');
      const response = await getApiClient().get<{ wallets: WalletBalance[]; totalUsdValue: number }>(
        API_ENDPOINTS.WALLET.GET
      );

      if (response.success && response.data) {
        log('Successfully fetched wallet balances from backend');
        return response.data.wallets.map(wallet => ({
          ...wallet,
          name: EAST_AFRICAN_CONFIG.CRYPTOCURRENCIES[wallet.currency as keyof typeof EAST_AFRICAN_CONFIG.CRYPTOCURRENCIES]?.name || wallet.currency,
          network: EAST_AFRICAN_CONFIG.CRYPTOCURRENCIES[wallet.currency as keyof typeof EAST_AFRICAN_CONFIG.CRYPTOCURRENCIES]?.network || 'Unknown',
        }));
      } else {
        throw new Error(response.message || 'Failed to get wallet balances');
      }
    } catch (error) {
      // Only log errors for authenticated users
      if (error.message !== 'UNAUTHENTICATED') {
        logError('Get wallet balances error:', error);
      }

      // Return mock data when API fails
      logWarn('API failed, returning mock wallet balances');
      return this.getMockWalletBalances();
    }
  }

  /**
   * Get mock wallet balances (extracted to separate method)
   */
  private getMockWalletBalances(): WalletBalance[] {
    return [
      {
        currency: 'USDT',
        name: 'Tether USD',
        network: 'Ethereum',
        balance: 1250.50,
        pendingBalance: 0,
        usdValue: 1250.50,
        change: 0.02,
        address: '******************************************',
      },
      {
        currency: 'BTC',
        name: 'Bitcoin',
        network: 'Bitcoin',
        balance: 0.05432,
        pendingBalance: 0,
        usdValue: 3456.78,
        change: 2.45,
        address: '******************************************',
      },
      {
        currency: 'ETH',
        name: 'Ethereum',
        network: 'Ethereum',
        balance: 0.8765,
        pendingBalance: 0,
        usdValue: 2234.56,
        change: -1.23,
        address: '******************************************',
      },
    ];
  }

  /**
   * Get specific wallet balance
   */
  async getWalletBalance(currency: string): Promise<WalletBalance> {
    try {
      log(`Fetching balance for ${currency}`);

      // Try to get specific balance from backend first
      const isAuth = await getApiClient().isAuthenticated();
      if (isAuth) {
        try {
          const response = await getApiClient().get<WalletBalance>(
            `${API_ENDPOINTS.WALLET.BALANCE}/${currency}`
          );

          if (response.success && response.data) {
            log(`Successfully fetched ${currency} balance from backend`);
            return {
              ...response.data,
              name: EAST_AFRICAN_CONFIG.CRYPTOCURRENCIES[currency as keyof typeof EAST_AFRICAN_CONFIG.CRYPTOCURRENCIES]?.name || currency,
              network: EAST_AFRICAN_CONFIG.CRYPTOCURRENCIES[currency as keyof typeof EAST_AFRICAN_CONFIG.CRYPTOCURRENCIES]?.network || 'Unknown',
            };
          }
        } catch (specificError) {
          logError(`Error fetching specific ${currency} balance:`, specificError);
          // Fall back to getting all balances
        }
      }

      // Fallback: get all balances and filter
      const balances = await this.getWalletBalances();
      const wallet = balances.find(w => w.currency === currency);

      if (!wallet) {
        throw new Error(`Wallet not found for currency: ${currency}`);
      }

      return wallet;
    } catch (error) {
      logError('Get wallet balance error:', error);
      throw error;
    }
  }

  /**
   * Get transaction history
   */
  async getTransactions(limit?: number, offset?: number): Promise<Transaction[]> {
    try {
      // Check if user is authenticated before making API call
      const isAuth = await getApiClient().isAuthenticated();
      if (!isAuth) {
        // Silently return mock data without error logging for unauthenticated users
        logWarn('User not authenticated, returning mock transaction history');
        return this.getMockTransactions();
      }

      log('Fetching transaction history from backend');
      const response = await getApiClient().get<Transaction[]>(
        API_ENDPOINTS.WALLET.TRANSACTIONS,
        { limit, offset }
      );

      if (response.success && response.data) {
        log(`Successfully fetched ${response.data.length} transactions from backend`);
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to get transactions');
      }
    } catch (error) {
      // Only log errors for authenticated users
      if (error.message !== 'UNAUTHENTICATED') {
        logError('Get transactions error:', error);
      }

      // Return mock transaction data when API fails
      logWarn('API failed, returning mock transaction history');
      return this.getMockTransactions();
    }
  }

  /**
   * Get mock transactions (extracted to separate method)
   */
  private getMockTransactions(): Transaction[] {
    log('Using mock transaction data');
    return [
      {
        _id: 'tx1',
        type: 'receive',
        cryptocurrency: 'BTC',
        amount: 0.0025,
        usdValue: 156.78,
        status: 'confirmed',
        txHash: '******************************************',
        fromAddress: '******************************************',
        toAddress: '******************************************',
        networkFee: 0.00001,
        description: 'P2P Trade Payment',
        timestamp: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        confirmations: 6,
        requiredConfirmations: 6,
      },
      {
        _id: 'tx2',
        type: 'send',
        cryptocurrency: 'USDT',
        amount: 500,
        usdValue: 500,
        status: 'confirmed',
        txHash: '0x8f3c2a1b9e7d6c5a4f3e2d1c0b9a8f7e6d5c4b3a2f1e',
        fromAddress: '******************************************',
        toAddress: '******************************************',
        networkFee: 2.5,
        description: 'Trade Settlement',
        timestamp: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
        confirmations: 12,
        requiredConfirmations: 12,
      },
      {
        _id: 'tx3',
        type: 'trade',
        cryptocurrency: 'ETH',
        amount: 0.5,
        usdValue: 1234.56,
        status: 'pending',
        txHash: '0x9a8b7c6d5e4f3a2b1c0d9e8f7a6b5c4d3e2f1a0b9c8d',
        fromAddress: '******************************************',
        toAddress: '******************************************',
        networkFee: 0.002,
        tradeId: 'trade_123',
        description: 'Escrow Release',
        timestamp: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        confirmations: 2,
        requiredConfirmations: 12,
      },
    ].slice(0, limit || 50);
  },

  /**
   * Get specific transaction by ID
   */
  async getTransaction(txId: string): Promise<Transaction> {
    try {
      const transactions = await this.getTransactions();
      const transaction = transactions.find(tx => tx._id === txId || tx.txHash === txId);

      if (!transaction) {
        throw new Error(`Transaction not found: ${txId}`);
      }

      return transaction;
    } catch (error) {
      console.error('Get transaction error:', error);
      throw error;
    }
  },

  /**
   * Send cryptocurrency
   */
  async sendCrypto(
    currency: string,
    toAddress: string,
    amount: number,
    note?: string
  ): Promise<Transaction> {
    try {
      const response = await getApiClient().post<Transaction>(
        `${API_ENDPOINTS.WALLET.GET}/send`,
        {
          currency,
          toAddress,
          amount,
          note,
        }
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to send cryptocurrency');
      }
    } catch (error) {
      console.error('Send crypto error:', error);
      throw error;
    }
  },

  /**
   * Get wallet security status
   */
  async getWalletSecurity(): Promise<WalletSecurity> {
    try {
      const response = await getApiClient().get<WalletSecurity>(
        API_ENDPOINTS.WALLET.SECURITY
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to get wallet security');
      }
    } catch (error) {
      console.error('Get wallet security error:', error);
      throw error;
    }
  }

  /**
   * Update wallet backup status
   */
  async updateBackupStatus(status: 'backed_up' | 'verified'): Promise<void> {
    try {
      const response = await getApiClient().put(
        `${API_ENDPOINTS.WALLET.SECURITY}/backup`,
        { status }
      );

      if (!response.success) {
        throw new Error(response.message || 'Failed to update backup status');
      }
    } catch (error) {
      console.error('Update backup status error:', error);
      throw error;
    }
  },

  /**
   * Get wallet statistics
   */
  async getWalletStats(): Promise<WalletStats> {
    try {
      const response = await getApiClient().get<WalletStats>(
        `${API_ENDPOINTS.WALLET.GET}/stats`
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to get wallet stats');
      }
    } catch (error) {
      console.error('Get wallet stats error:', error);
      throw error;
    }
  },

  /**
   * Generate new receiving address
   */
  async generateReceiveAddress(currency: string): Promise<string> {
    try {
      const response = await getApiClient().post<{ address: string }>(
        `${API_ENDPOINTS.WALLET.GET}/generate-address`,
        { currency }
      );

      if (response.success && response.data) {
        return response.data.address;
      } else {
        throw new Error(response.message || 'Failed to generate address');
      }
    } catch (error) {
      console.error('Generate address error:', error);
      throw error;
    }
  },

  /**
   * Validate cryptocurrency address
   */
  async validateAddress(currency: string, address: string): Promise<boolean> {
    try {
      const response = await getApiClient().post<{ valid: boolean }>(
        `${API_ENDPOINTS.WALLET.GET}/validate-address`,
        { currency, address }
      );

      return response.success && response.data?.valid === true;
    } catch (error) {
      console.error('Validate address error:', error);
      return false;
    }
  },

  /**
   * Estimate transaction fee
   */
  async estimateFee(currency: string, amount: number, toAddress: string): Promise<number> {
    try {
      const response = await getApiClient().post<{ fee: number }>(
        `${API_ENDPOINTS.WALLET.GET}/estimate-fee`,
        { currency, amount, toAddress }
      );

      if (response.success && response.data) {
        return response.data.fee;
      } else {
        throw new Error(response.message || 'Failed to estimate fee');
      }
    } catch (error) {
      console.error('Estimate fee error:', error);
      throw error;
    }
  }
}

// Create and export service instance
export const walletService = new WalletService();

// Wallet utility functions
export const walletUtils = {
  /**
   * Format cryptocurrency amount with proper decimals
   */
  formatCryptoAmount(amount: number, currency: string): string {
    const decimals = ['BTC', 'ETH'].includes(currency) ? 8 : 6;
    return amount.toFixed(decimals);
  },

  /**
   * Format USD value
   */
  formatUsdValue(amount: number): string {
    return `$${amount.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  },

  /**
   * Format percentage change
   */
  formatPercentageChange(change: number): string {
    const sign = change >= 0 ? '+' : '';
    return `${sign}${change.toFixed(2)}%`;
  },

  /**
   * Get transaction status color
   */
  getTransactionStatusColor(status: Transaction['status']): string {
    switch (status) {
      case 'confirmed':
        return '#10B981'; // Green
      case 'pending':
        return '#F59E0B'; // Yellow
      case 'failed':
        return '#EF4444'; // Red
      default:
        return '#6B7280'; // Gray
    }
  },

  /**
   * Get transaction type icon
   */
  getTransactionTypeIcon(type: Transaction['type']): string {
    switch (type) {
      case 'send':
        return '↗️';
      case 'receive':
        return '↙️';
      case 'trade':
        return '🔄';
      case 'escrow':
        return '🔒';
      default:
        return '💰';
    }
  },

  /**
   * Calculate total portfolio value
   */
  calculateTotalValue(wallets: WalletBalance[]): number {
    return wallets.reduce((total, wallet) => total + wallet.usdValue, 0);
  },

  /**
   * Get wallet by currency
   */
  getWalletByCurrency(wallets: WalletBalance[], currency: string): WalletBalance | undefined {
    return wallets.find(wallet => wallet.currency === currency);
  },

  /**
   * Sort wallets by value (descending)
   */
  sortWalletsByValue(wallets: WalletBalance[]): WalletBalance[] {
    return [...wallets].sort((a, b) => b.usdValue - a.usdValue);
  },

  /**
   * Filter wallets with balance > 0
   */
  getWalletsWithBalance(wallets: WalletBalance[]): WalletBalance[] {
    return wallets.filter(wallet => wallet.balance > 0);
  },

  /**
   * Truncate wallet address for display
   */
  truncateAddress(address: string, startChars = 6, endChars = 4): string {
    if (address.length <= startChars + endChars) return address;
    return `${address.slice(0, startChars)}...${address.slice(-endChars)}`;
  },
};

export default walletService;
