import React, { createContext, useContext, useState, useEffect } from 'react';
import { apiClient, API_ENDPOINTS } from '../services/api';
import { websocketService } from '../services/websocketService';
import { log, logError, logWarn } from '../config/environment';

// Notification types
export type NotificationType =
  | 'trade_request'
  | 'payment_received'
  | 'trade_completed'
  | 'security_alert'
  | 'kyc_update'
  | 'system_update';

// Notification interface
export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  relatedId?: string;
  icon: string;
  iconColor: string;
}

// Notification context interface
interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  clearAll: () => void;
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'isRead'>) => void;
  refreshNotifications: () => Promise<void>;
}

// Mock notifications data
const mockNotifications: Notification[] = [
  {
    id: '1',
    type: 'trade_request',
    title: 'New Trade Request',
    message: 'John Doe wants to buy 0.005 BTC from you',
    timestamp: '2024-01-15T14:30:00Z',
    isRead: false,
    relatedId: 'trade_123',
    icon: 'swap-horizontal',
    iconColor: 'primary',
  },
  {
    id: '2',
    type: 'payment_received',
    title: 'Payment Received',
    message: 'Payment of $250 has been confirmed for trade #456',
    timestamp: '2024-01-15T13:15:00Z',
    isRead: false,
    relatedId: 'trade_456',
    icon: 'checkmark-circle',
    iconColor: 'success',
  },
  {
    id: '3',
    type: 'kyc_update',
    title: 'KYC Verification Approved',
    message: 'Your identity verification has been approved. You can now trade up to $10,000.',
    timestamp: '2024-01-15T12:45:00Z',
    isRead: false,
    relatedId: null,
    icon: 'shield-checkmark',
    iconColor: 'success',
  },
  {
    id: '4',
    type: 'trade_completed',
    title: 'Trade Completed',
    message: 'Your trade with Alice Johnson has been completed successfully',
    timestamp: '2024-01-15T12:00:00Z',
    isRead: true,
    relatedId: 'trade_789',
    icon: 'trophy',
    iconColor: 'warning',
  },
  {
    id: '5',
    type: 'security_alert',
    title: 'New Login Detected',
    message: 'A new login was detected from Chrome on Windows',
    timestamp: '2024-01-14T20:30:00Z',
    isRead: true,
    relatedId: null,
    icon: 'shield-outline',
    iconColor: 'error',
  },
  {
    id: '6',
    type: 'system_update',
    title: 'System Update',
    message: 'New features available! Check out the latest improvements',
    timestamp: '2024-01-14T18:00:00Z',
    isRead: true,
    relatedId: null,
    icon: 'information-circle',
    iconColor: 'textSecondary',
  },
];

// Create the context
const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

// Notification Provider component
export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Calculate unread count
  const unreadCount = notifications.filter(n => !n.isRead).length;

  // Mark a single notification as read
  const markAsRead = async (notificationId: string) => {
    try {
      // Update backend
      const isAuth = await apiClient.isAuthenticated();
      if (isAuth) {
        await apiClient.put(`${API_ENDPOINTS.NOTIFICATIONS.MARK_READ}/${notificationId}`);
      }

      // Update local state
      setNotifications(prev =>
        prev.map(n =>
          n.id === notificationId ? { ...n, isRead: true } : n
        )
      );
    } catch (error) {
      logError('Failed to mark notification as read:', error);
      // Still update local state even if backend fails
      setNotifications(prev =>
        prev.map(n =>
          n.id === notificationId ? { ...n, isRead: true } : n
        )
      );
    }
  };

  // Mark all notifications as read
  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(n => ({ ...n, isRead: true }))
    );
  };

  // Clear all notifications
  const clearAll = () => {
    setNotifications([]);
  };

  // Add a new notification
  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'isRead'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      isRead: false,
    };

    setNotifications(prev => [newNotification, ...prev]);
  };

  // Refresh notifications from backend
  const refreshNotifications = async () => {
    setIsLoading(true);
    try {
      log('Refreshing notifications from backend');

      // Check if user is authenticated
      const isAuth = await apiClient.isAuthenticated();
      if (!isAuth) {
        logWarn('User not authenticated, using mock notifications');
        setNotifications(mockNotifications);
        return;
      }

      // Fetch from backend
      const response = await apiClient.get<Notification[]>(API_ENDPOINTS.NOTIFICATIONS.LIST);

      if (response.success && response.data) {
        log(`Successfully fetched ${response.data.length} notifications`);
        setNotifications(response.data);
      } else {
        logWarn('API failed, using mock notifications');
        setNotifications(mockNotifications);
      }
    } catch (error) {
      logError('Failed to refresh notifications:', error);
      // Fallback to mock data
      setNotifications(mockNotifications);
    } finally {
      setIsLoading(false);
    }
  };

  // Set up WebSocket for real-time notifications
  useEffect(() => {
    // Initial fetch
    refreshNotifications();

    // Set up WebSocket listener for real-time notifications
    const handleNotification = (data: any) => {
      if (data.type === 'notification') {
        log('Received real-time notification:', data);

        // Add the notification to our state
        addNotification({
          type: data.payload.type as NotificationType,
          title: data.payload.title,
          message: data.payload.message,
          icon: data.payload.icon || getIconForNotificationType(data.payload.type),
          iconColor: data.payload.iconColor || 'primary',
          relatedId: data.payload.relatedId,
        });
      }
    };

    // Subscribe to WebSocket notifications
    websocketService.subscribe('notification', handleNotification);

    // Connect to WebSocket if not already connected
    if (!websocketService.isConnected()) {
      websocketService.connect().catch(err => logError('WebSocket connection failed:', err));
    }

    // Clean up on unmount
    return () => {
      websocketService.unsubscribe('notification', handleNotification);
    };
  }, []);

  // Helper function to get icon for notification type
  const getIconForNotificationType = (type: NotificationType): string => {
    switch (type) {
      case 'trade_request': return 'swap-horizontal';
      case 'payment_received': return 'cash';
      case 'trade_completed': return 'checkmark-circle';
      case 'security_alert': return 'shield';
      case 'kyc_update': return 'person-circle';
      case 'system_update': return 'information-circle';
      default: return 'notifications';
    }
  };

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    isLoading,
    markAsRead,
    markAllAsRead,
    clearAll,
    addNotification,
    refreshNotifications,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

// Hook to use notification context
export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};
